# RAG服务优化配置
# 减少relationships复杂度和存储冗余

from llama_index.core.node_parser import SentenceSplitter
from llama_index.core import Settings

def setup_optimized_node_parser():
    """设置优化的节点解析器"""
    Settings.node_parser = SentenceSplitter(
        chunk_size=512,  # 增大块大小减少总块数
        chunk_overlap=30,  # 减少重叠降低关系复杂度
        separator="\n\n",  # 使用段落分隔符
        include_metadata=True,
        include_prev_next_rel=False,  # 禁用前后关系
    )

def get_optimized_metadata_keys():
    """获取优化的元数据字段列表"""
    # 只保留必要的元数据字段
    essential_keys = [
        "filename",
        "content_id", 
        "title",
        "source",
        "file_url"
    ]
    return essential_keys
